"""
飞书文档搜索相关工具。
"""

import uuid
import aiohttp
import json
from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager


async def search_feishu_docs(
    wrapper: RunContextWrapper[UserInfo], 
    query: str, 
    page_size: int = 20,
    get_content: bool = True
) -> Tuple[List[Dict[str, Any]], str]:
    """搜索飞书文档并获取内容。

    该工具用于在飞书知识库中搜索相关文档，可以帮助AI获取公司内部的业务知识和规则定义。
    工作流程：
    1. 使用搜索接口根据关键词查找相关文档
    2. 如果get_content为True，会进一步调用详情接口获取每个文档的具体内容
    3. 返回包含文档标题、链接和内容的完整信息

    Args:
        wrapper: 包含用户信息的上下文包装器。
        query: 搜索关键词，建议使用具体的业务术语或问题描述。
        page_size: 返回结果数量，默认为20，最大不超过50。
        get_content: 是否获取文档详细内容，默认为True。如果为False，只返回搜索结果列表。

    Returns:
        Tuple[List[Dict[str, Any]], str]: 搜索结果列表和描述信息。
        搜索结果包含：title（标题）、url（链接）、obj_token（文档标识）、content（内容，仅当get_content=True时）
    """
    user_info = wrapper.context
    access_token = user_info.access_token
    
    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"
    
    search_id = str(uuid.uuid4())
    logger.info(f"用户开始搜索飞书文档，搜索ID: {search_id}, 关键词: {query}, access_token: {access_token[:20]}...")
    
    try:
        # 调用搜索接口
        search_url = f"https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size={page_size}"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }
        search_data = {'query': query}
        
        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(search_url, headers=headers, json=search_data) as response:
                if response.status != 200:
                    error_msg = f"搜索接口调用失败，状态码: {response.status}, 响应内容: {await response.text()}"
                    logger.error(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"
                
                search_result = await response.json()
                
                if search_result.get('code') != 0:
                    error_msg = f"搜索接口返回错误: {search_result.get('msg', '未知错误')}"
                    logger.error(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"
                
                items = search_result.get('data', {}).get('items', [])
                for item in items:
                    logger.info(f"搜索到文档: {item['title']}, 链接: {item['url']}")
                
                if not items:
                    return [], f"未找到与关键词 '{query}' 相关的文档"
                
                # 如果不需要获取内容，直接返回搜索结果
                if not get_content:
                    return items, f"成功搜索到 {len(items)} 个相关文档"
                
                # 获取每个文档的详细内容
                enriched_items = []
                for item in items:
                    obj_token = item.get('obj_token')
                    if obj_token:
                        content = await _get_feishu_doc_content(access_token, obj_token)
                        item['content'] = content
                    enriched_items.append(item)
                
                return enriched_items, f"成功搜索到 {len(enriched_items)} 个相关文档并获取了内容"
                
    except Exception as e:
        error_msg = f"搜索飞书文档时发生异常: {str(e)}"
        logger.error(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"


async def get_feishu_doc_content_tool(
    wrapper: RunContextWrapper[UserInfo], 
    obj_token: str
) -> Tuple[Optional[str], str]:
    """获取指定飞书文档的纯文本内容。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        obj_token: 文档的obj_token标识符。

    Returns:
        Tuple[Optional[str], str]: 文档内容和描述信息。
    """
    user_info = wrapper.context
    access_token = user_info.access_token
    
    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return None, f"获取文档内容失败: {error_msg}"
    
    logger.info(f"开始获取飞书文档内容，obj_token: {obj_token}")
    
    content = await _get_feishu_doc_content(access_token, obj_token)
    
    if content:
        return content, f"成功获取文档内容，长度: {len(content)} 字符"
    else:
        return None, f"获取文档内容失败，obj_token: {obj_token}"


async def _get_feishu_doc_content(access_token: str, obj_token: str) -> Optional[str]:
    """内部函数：获取飞书文档的纯文本内容。
    
    Args:
        access_token: 飞书访问令牌。
        obj_token: 文档的obj_token标识符。
        
    Returns:
        Optional[str]: 文档的纯文本内容，获取失败时返回None。
    """
    try:
        logger.info(f"正在获取飞书文档内容，obj_token: {obj_token}, access_token: {access_token[:20]}...")
        content_url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{obj_token}/raw_content?lang=0"
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(content_url, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"获取文档内容失败，状态码: {response.status}, obj_token: {obj_token}, 响应内容: {await response.text()}")
                    return None
                
                content_result = await response.json()
                
                if content_result.get('code') != 0:
                    logger.error(f"获取文档内容接口返回错误: {content_result.get('msg', '未知错误')}, obj_token: {obj_token}")
                    return None
                
                content = content_result.get('data', {}).get('content', '')
                return content
                
    except Exception as e:
        logger.error(f"获取文档内容时发生异常: {str(e)}, obj_token: {obj_token}")
        return None


# 注册工具
tool_manager.register_as_function_tool(search_feishu_docs)
tool_manager.register_as_function_tool(get_feishu_doc_content_tool)