"""
Data fetcher bot implementation.
"""

import yaml
import textwrap
import json # Added import
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Callable

from agents import Agent, Model, ModelSettings

from src.models.user_info_class import UserInfo
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import (
    OPENAI_MODEL_SETTINGS,
    CACHE_ENABLED_CLAUDE_MODEL,
    get_model_for_name
)
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.resource_manager import load_resource
from src.utils.logger import logger


def _load_config(config_file: str) -> Dict[str, Any]:
    """
    Load the agent configuration from a YAML file.

    Args:
        config_file: Name of the YAML configuration file

    Returns:
        Dict containing the configuration
    """
    # 使用 resource_manager 加载配置文件
    yaml_content = load_resource("data_fetcher_bot_config", config_file)

    if yaml_content:
        try:
            return yaml.safe_load(yaml_content)
        except Exception as e:
            # 如果 YAML 解析失败，记录错误并返回默认配置
            logger.error(f"Error parsing YAML config file {config_file}: {e}")
    else:
        logger.error(
            f"Config file {config_file} not found in data_fetcher_bot_config directory"
        )

    # 如果加载或解析失败，返回默认配置
    return {
        "agent_name": "sales_order_analytics",
        "agent_description": "销售订单分析专家",
        "agent_tables": [],
    }


class DataFetcherBot(BaseBot):
    """
    Data fetcher bot for retrieving data via SQL queries.

    This bot is specialized in:
    - Fetching DDL information for specific tables
    - Writing and executing SQL queries
    - Retrieving sample data from tables

    The bot can be configured with different YAML files to specialize in different domains.
    """

    def __init__(
        self, user_info: Dict[str, Any], config_file: str = "sales_orders.yml"
    ):
        super().__init__(user_info)
        self.config_file = config_file
        self.config = _load_config(config_file)
        self.table_with_desc = [
            f"- {t.get('name', 'N/A')}: {t.get('desc', 'N/A')}"
            for t in self.config.get("agent_tables", [])
        ]
        self.table_with_desc = "\n".join(self.table_with_desc)

    def get_description(self) -> str:
        agent_name = self.config.get("agent_name", "特定领域")
        agent_description = self.config.get("agent_description", "未提供描述")

        return f"我是一个专注于{agent_name}分析的专家，负责业务:\n{agent_description}\n我可以处理以下相关数据表: {self.table_with_desc}"

    def create_agent(self, model: Optional[Model] = None, model_settings_str: Optional[str] = None) -> Agent:
        agent_name = self.config.get("agent_name", "特定领域")
        agent_description = self.config.get("agent_description", "")
        tools = self.config.get("tools", [])

        # 从配置文件读取model配置，如果没有则使用传入的参数或默认值
        config_model = self.config.get("model")
        config_model_settings = self.config.get("model_settings")

        logger.info(f"agent_name:{agent_name}, tools:{tools}, config_model:{config_model}, config_model_settings:{config_model_settings}")
        tool_list = tool_manager.get_tool_list([tool['name'] for tool in tools])

        system_instruction = load_resource("prompt", "data_fetcher_instruction.md")
        domain_instruction = textwrap.dedent(
            f"""
            你是一个专注于 {agent_name} 分析的专家。
            核心业务:
            {agent_description}
            相关数据表:
            {self.table_with_desc}
        """
        ).strip()

        # Create realtime instruction with user context
        realtime_instruction = self.get_user_realtime_instruction()

        instruction = (
            f"{system_instruction}" f"{domain_instruction}" f"{realtime_instruction}"
        )

        # 确定使用的模型（带异常处理）
        final_model = self._safe_get_model(config_model, model)

        # 处理model_settings（带异常处理）
        model_settings = self._safe_get_model_settings(config_model_settings, model_settings_str)

        # 创建Agent
        agent_kwargs = {
            "name": f"{agent_name}_specialist",
            "instructions": instruction,
            "model": final_model,
            "tools": tool_list
        }

        if model_settings:
            agent_kwargs["model_settings"] = model_settings

        return Agent[UserInfo](**agent_kwargs)

    def _safe_get_model(self, config_model: str, fallback_model: Optional[Model]) -> Model:
        """安全获取模型实例，优先使用配置文件中的模型"""
        if config_model:
            try:
                model_instance = get_model_for_name(config_model)
                logger.info(f"使用配置文件中的模型: {config_model}")
                return model_instance
            except Exception as e:
                logger.warning(f"配置模型 {config_model} 创建失败: {e}，使用默认模型")

        return fallback_model or CACHE_ENABLED_CLAUDE_MODEL

    def _safe_get_model_settings(self, config_settings: Any, fallback_settings: Optional[str]) -> Optional[ModelSettings]:
        """安全获取模型设置，优先使用配置文件中的设置"""
        settings_source = config_settings or fallback_settings or OPENAI_MODEL_SETTINGS

        if not settings_source or settings_source == "" or settings_source == "{}":
            return None

        try:
            # 处理字典格式的配置
            if isinstance(settings_source, dict):
                logger.info(f"使用配置文件中的model_settings: {settings_source}")
                return ModelSettings(**settings_source)

            # 处理JSON字符串格式
            settings_dict = json.loads(settings_source)
            logger.info(f"解析model_settings成功: {settings_dict}")
            return ModelSettings(**settings_dict)
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"解析model_settings失败: {e}，跳过设置")
            return None
