"""飞书Token服务模块
负责管理飞书API的Token获取和管理
"""

import requests
from datetime import datetime, timedelta
from typing import Optional, Tuple
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET
from src.utils.in_memory_cache import in_memory_cache
from src.services.auth.user_session_service import user_session_service


class TokenService:
    """飞书Token服务类"""

    @staticmethod
    @in_memory_cache(expire_seconds=3600)
    def get_feishu_token() -> dict:
        """获取飞书访问Token

        Returns:
            dict: 包含Token信息的字典
        """
        url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
        try:
            token = requests.post(
                url=url,
                json={"app_id": APP_ID, "app_secret": APP_SECRET},
            ).json()
            logger.info(f"feishu tenant_token:{token}")
            return token
        except Exception as e:
            logger.error(f"获取飞书Token失败: {e}", exc_info=True)
            return {}

    @staticmethod
    def get_tenant_access_token() -> str:
        """获取租户访问Token

        Returns:
            str: 租户访问Token，如果获取失败返回空字符串
        """
        token_info = TokenService.get_feishu_token()
        return token_info.get("tenant_access_token", "")
    
    @staticmethod
    def get_user_access_token(open_id: str) -> Optional[str]:
        """获取用户的有效access token
        
        如果token即将过期或已过期，会自动刷新
        
        Args:
            open_id: 用户的飞书open_id
            
        Returns:
            Optional[str]: 有效的access token，获取失败返回None
        """
        try:
            # 查找用户的活跃session
            sessions = user_session_service.repository.find_active_sessions_by_open_id(open_id)
            if not sessions:
                logger.warning(f"未找到用户的活跃session: open_id={open_id}")
                return None
            
            # 使用最新的session
            session = sessions[0]
            
            # 检查access token是否即将过期（提前10分钟刷新）
            if session.access_token_expires_at:
                time_until_expiry = (session.access_token_expires_at - datetime.now()).total_seconds() / 60
                if time_until_expiry <= 10:  # 10分钟内过期
                    logger.info(f"用户access token即将过期，开始刷新: open_id={open_id}")
                    new_access_token = TokenService._refresh_user_access_token(session.session_id, session.refresh_token)
                    if new_access_token:
                        return new_access_token
                    else:
                        logger.error(f"刷新用户access token失败，可能需要重新登录: open_id={open_id}")
                        return None
                elif time_until_expiry < 0:  # 已过期
                    logger.warning(f"用户access token已过期: open_id={open_id}")
                    return None
            
            return session.access_token
            
        except Exception as e:
            logger.error(f"获取用户access token失败: {e}", exc_info=True)
            return None
    
    @staticmethod
    def _refresh_user_access_token(session_id: str, refresh_token: str) -> Optional[str]:
        """刷新用户的access token
        
        Args:
            session_id: session ID
            refresh_token: 用于刷新的refresh token
            
        Returns:
            Optional[str]: 新的access token，刷新失败返回None
        """
        url = "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token"
        
        try:
            # 获取app access token用于刷新用户token
            app_token_info = TokenService.get_feishu_token()
            app_access_token = app_token_info.get("app_access_token")
            
            if not app_access_token:
                logger.error("无法获取app access token用于刷新用户token")
                return None
            
            headers = {
                "Authorization": f"Bearer {app_access_token}",
                "Content-Type": "application/json"
            }
            
            data = {
                "grant_type": "refresh_token",
                "refresh_token": refresh_token
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    new_access_token = result["data"]["access_token"]
                    new_refresh_token = result["data"]["refresh_token"]
                    expires_in = result["data"]["expires_in"]  # 秒数
                    
                    # 计算过期时间
                    expires_at = datetime.now() + timedelta(seconds=expires_in)
                    
                    # 更新session中的token信息
                    success = user_session_service.update_session_tokens(
                        session_id, new_access_token, new_refresh_token, expires_at
                    )
                    
                    if success:
                        logger.info(f"用户access token刷新成功: session_id={session_id}")
                        return new_access_token
                    else:
                        logger.error(f"更新session中的token信息失败: session_id={session_id}")
                        return None
                else:
                    error_code = result.get("code")
                    error_msg = result.get("msg", "")
                    logger.error(f"刷新用户access token失败: code={error_code}, msg={error_msg}")
                    
                    # 如果是refresh token失效，将session标记为无效
                    if error_code == 20026:  # refresh token not found
                        logger.warning(f"refresh token已失效，将session标记为无效: session_id={session_id}")
                        try:
                            user_session_service.invalidate_session(session_id)
                        except Exception as e:
                            logger.error(f"标记session无效时出错: {e}")
                    
                    return None
            else:
                logger.error(f"刷新用户access token请求失败: status={response.status_code}, response={response.text}")
                return None
                
        except Exception as e:
            logger.error(f"刷新用户access token时出错: {e}", exc_info=True)
            return None
