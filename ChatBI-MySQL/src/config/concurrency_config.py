"""
并发配置模块

该模块定义了系统中各种并发控制的配置参数。
"""

import os
from typing import Dict, Any


class ConcurrencyConfig:
    """并发配置类"""
    
    # 用户查询限制配置
    MAX_CONCURRENT_QUERIES_PER_USER = int(os.getenv("MAX_CONCURRENT_QUERIES_PER_USER", "3"))
    MAX_QUERY_TIME_SECONDS = int(os.getenv("MAX_QUERY_TIME_SECONDS", "600"))  # 10分钟
    SLOW_QUERY_THRESHOLD_SECONDS = int(os.getenv("SLOW_QUERY_THRESHOLD_SECONDS", "30"))
    
    # 线程池配置
    DB_QUERY_THREAD_POOL_SIZE = int(os.getenv("DB_QUERY_THREAD_POOL_SIZE", "30"))
    FEISHU_MESSAGE_THREAD_POOL_SIZE = int(os.getenv("FEISHU_MESSAGE_THREAD_POOL_SIZE", "50"))
    
    # 数据库连接池配置
    BUSINESS_DB_POOL_SIZE = int(os.getenv("BUSINESS_MYSQL_POOL_SIZE", "20"))
    CHATBI_DB_POOL_SIZE = int(os.getenv("CHATBI_MYSQL_POOL_SIZE", "10"))
    
    # 连接池获取超时配置
    BUSINESS_POOL_GET_TIMEOUT = int(os.getenv("BUSINESS_POOL_GET_TIMEOUT", "30"))
    CHATBI_POOL_GET_TIMEOUT = int(os.getenv("CHATBI_POOL_GET_TIMEOUT", "30"))
    
    # 监控配置
    DB_POOL_MONITOR_INTERVAL = int(os.getenv("DB_POOL_MONITOR_INTERVAL", "30"))
    ENABLE_DB_POOL_MONITORING = os.getenv("ENABLE_DB_POOL_MONITORING", "true").lower() == "true"
    
    @classmethod
    def get_config_summary(cls) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "user_query_limits": {
                "max_concurrent_per_user": cls.MAX_CONCURRENT_QUERIES_PER_USER,
                "max_query_time_seconds": cls.MAX_QUERY_TIME_SECONDS,
                "slow_query_threshold_seconds": cls.SLOW_QUERY_THRESHOLD_SECONDS
            },
            "thread_pools": {
                "db_query_pool_size": cls.DB_QUERY_THREAD_POOL_SIZE,
                "feishu_message_pool_size": cls.FEISHU_MESSAGE_THREAD_POOL_SIZE
            },
            "database_pools": {
                "business_db_pool_size": cls.BUSINESS_DB_POOL_SIZE,
                "chatbi_db_pool_size": cls.CHATBI_DB_POOL_SIZE,
                "business_pool_get_timeout": cls.BUSINESS_POOL_GET_TIMEOUT,
                "chatbi_pool_get_timeout": cls.CHATBI_POOL_GET_TIMEOUT
            },
            "monitoring": {
                "db_pool_monitor_interval": cls.DB_POOL_MONITOR_INTERVAL,
                "enable_db_pool_monitoring": cls.ENABLE_DB_POOL_MONITORING
            }
        }
    
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """验证配置参数的合理性"""
        issues = []
        warnings = []
        
        # 检查用户查询限制
        if cls.MAX_CONCURRENT_QUERIES_PER_USER > 10:
            warnings.append("每用户最大并发查询数过高，可能影响系统性能")
        
        if cls.MAX_QUERY_TIME_SECONDS > 600:  # 10分钟
            warnings.append("查询超时时间过长，可能导致资源占用过久")
        
        # 检查线程池大小
        if cls.DB_QUERY_THREAD_POOL_SIZE < cls.BUSINESS_DB_POOL_SIZE:
            issues.append("数据库查询线程池大小应该不小于数据库连接池大小")
        
        if cls.FEISHU_MESSAGE_THREAD_POOL_SIZE < 20:
            warnings.append("飞书消息处理线程池可能过小，建议至少20个线程")
        
        # 检查数据库连接池
        if cls.BUSINESS_DB_POOL_SIZE < 10:
            warnings.append("业务数据库连接池可能过小，建议至少10个连接")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings
        }


# 全局配置实例
concurrency_config = ConcurrencyConfig()
