"""
Main application file for ChatBI-MySQL.
"""
import argparse
import os
import threading
import asyncio

from dotenv import load_dotenv
from flask import Flask
from werkzeug.middleware.proxy_fix import ProxyFix

from src.api import register_routes
from src.services.feishu.client import start_feishu_client
from src.utils.logger import logger
from src.config.concurrency_config import ConcurrencyConfig
from src.services.monitoring.db_pool_monitor import start_db_monitoring
from src.services.auth.session_cleanup import start_session_cleanup_service

# --- Configuration ---
load_dotenv()
# 打印所有的环境变量
for key, value in os.environ.items():
    logger.info(f"{key}:{value}")

APPLICATION_ROOT = os.getenv("APPLICATION_ROOT", "/crm-chatbi")
ENABLE_BOT_MESSAGE_PROCESSING = os.getenv("ENABLE_BOT_MESSAGE_PROCESSING",
                                          "False").lower() == "true"
logger.info(f"APPLICATION_ROOT:{APPLICATION_ROOT}")

APP_NAME = "CRM-ChatBI-MySQL"
app = Flask(APP_NAME,
            static_folder="src/static",  # Updated path to static folder
            template_folder="src/templates"  # Updated path to templates folder
            )
# Apply ProxyFix to handle headers from Nginx
app.wsgi_app = ProxyFix(
    app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1
)
app.config["SEND_FILE_MAX_AGE_DEFAULT"] = 86400
app.secret_key = APP_NAME  # Used for session encryption
MEGABYTE = (2 ** 10) ** 2
app.config["MAX_CONTENT_LENGTH"] = None
app.config["MAX_FORM_MEMORY_SIZE"] = 20 * MEGABYTE

# Register all API routes
register_routes(app)

# --- Main Application Start ---
if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Run Flask application")

    # Add port parameter, default value is 5700
    parser.add_argument("--port", type=int, default=5700, help="Listen port")

    # Add debug mode parameter, default is True
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument(
        "--no-debug", action="store_false", dest="debug", help="Disable debug mode"
    )
    parser.set_defaults(debug=False)

    # Parse command line arguments
    args = parser.parse_args()

    # 打印并发配置信息
    config_summary = ConcurrencyConfig.get_config_summary()
    logger.info(f"并发配置: {config_summary}")

    # 验证配置
    config_validation = ConcurrencyConfig.validate_config()
    if not config_validation["valid"]:
        logger.error(f"配置验证失败: {config_validation['issues']}")
    if config_validation["warnings"]:
        logger.warning(f"配置警告: {config_validation['warnings']}")

    # Start Feishu client in a separate thread
    if ENABLE_BOT_MESSAGE_PROCESSING:
        feishu_thread = threading.Thread(target=start_feishu_client, daemon=True)
        feishu_thread.start()
        logger.info("Feishu client WebSocket connection started in background thread")

    # 启动数据库连接池监控（如果启用）
    if ConcurrencyConfig.ENABLE_DB_POOL_MONITORING:
        def start_monitoring():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(start_db_monitoring())

        monitoring_thread = threading.Thread(target=start_monitoring, daemon=True)
        monitoring_thread.start()
        logger.info("Database pool monitoring started in background thread")

    # 启动Session清理服务
    start_session_cleanup_service()
    logger.info("Session cleanup service started in background thread")

    # Run in development environment for debugging
    app.run(host="0.0.0.0", port=args.port, debug=args.debug)
