# Union ID 修复说明

## 问题描述

在移除 `access_token` cookie 依赖后，用户上传图片资源时失败。原因是上传资源需要调用 summerfarm 接口，该接口使用用户的 `union_id` 来获取 token，但 `union_id` 没有被保存到用户 session 中。

## 问题分析

1. **登录流程**：`get_user_info_from_feishu` 函数确实获取了 `union_id` 并用它来获取 `summerfarm_api_token`
2. **数据存储**：但是 `union_id` 没有被保存到数据库的 `user` 表中
3. **Session 获取**：当前 session 中的 `user_info` 是从数据库获取的，缺少 `union_id` 信息
4. **上传失败**：资源上传 API 无法从 session 中获取有效的 `summerfarm_api_token`

## 解决方案

### 1. 数据库结构修改

- 在 `user` 表中添加 `union_id` 字段
- 添加相应的索引以提高查询性能

### 2. 代码逻辑更新

- 更新 `upsert_user_info` 函数，保存 `union_id` 到数据库
- 更新 `get_user_info_by_open_id` 函数，从数据库读取 `union_id` 并动态获取 `summerfarm_api_token`

### 3. 自动 Token 获取

- 在获取用户信息时，如果存在 `union_id`，自动调用 `get_api_token` 获取 `summerfarm_api_token`
- 确保 session 中始终包含有效的 API token

## 部署步骤

### 1. 执行数据库迁移

```bash
# 执行迁移脚本（在应用重启前）
mysql -u [username] -p chatbi < migrate_add_union_id.sql
```

### 2. 重启应用

```bash
# 重启应用以加载新代码
./restart.sh
```

### 3. 验证修复

1. 用户重新登录（清除旧的 session 数据）
2. 尝试上传图片资源
3. 检查日志确认 `union_id` 和 `summerfarm_api_token` 正常获取

## 修改的文件

1. `init.sql` - 添加 `union_id` 字段定义
2. `src/services/auth/user_login_with_feishu.py` - 更新用户信息存储和获取逻辑
3. `migrate_add_union_id.sql` - 数据库迁移脚本

## 注意事项

1. **缓存清理**：用户信息有缓存，修改后会自动清除相关缓存
2. **向后兼容**：代码支持新旧数据格式，确保平滑迁移
3. **错误处理**：如果 `union_id` 为空，不会影响其他功能，只是无法获取 API token
4. **性能优化**：`get_api_token` 函数有缓存机制，避免频繁请求

## 测试建议

1. 测试新用户登录和图片上传
2. 测试现有用户重新登录后的图片上传
3. 验证其他功能不受影响
4. 检查日志中的 token 获取情况